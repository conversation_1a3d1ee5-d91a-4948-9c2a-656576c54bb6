package me.zxk.smartagriculture.common;

/**
 * 错误码枚举
 */
public enum ErrorCode {
    
    // 通用错误码
    SUCCESS(200, "操作成功"),
    SYSTEM_ERROR(500, "系统错误"),
    PARAM_ERROR(400, "参数错误"),
    
    // 认证相关错误码
    UNAUTHORIZED(401, "未登录或登录已过期"),
    FORBIDDEN(403, "权限不足"),
    LOGIN_FAILED(1001, "用户名或密码错误"),
    USER_NOT_FOUND(1002, "用户不存在"),
    USERNAME_EXISTS(1003, "用户名已存在"),
    PHONE_EXISTS(1004, "手机号已存在"),
    EMAIL_EXISTS(1005, "邮箱已存在"),
    PASSWORD_ERROR(1006, "密码错误"),
    TOKEN_INVALID(1007, "Token无效"),
    TOKEN_EXPIRED(1008, "Token已过期"),
    
    // 大棚相关错误码
    GREENHOUSE_NOT_FOUND(2001, "大棚不存在"),
    GREENHOUSE_ACCESS_DENIED(2002, "无权访问该大棚"),
    GREENHOUSE_NAME_EXISTS(2003, "大棚名称已存在"),
    
    // 文件上传相关错误码
    FILE_UPLOAD_ERROR(3001, "文件上传失败"),
    FILE_TYPE_ERROR(3002, "文件类型不支持"),
    FILE_SIZE_ERROR(3003, "文件大小超出限制"),
    
    // 天气服务相关错误码
    WEATHER_SERVICE_ERROR(4001, "天气服务异常"),
    LOCATION_ERROR(4002, "位置信息错误");

    private final Integer code;
    private final String message;

    ErrorCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
