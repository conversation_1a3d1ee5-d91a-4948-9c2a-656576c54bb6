package me.zxk.smartagriculture.dto.greenhouse;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 大棚列表响应DTO
 */
@Data
public class GreenhouseListResponse {

    private Long id;
    private String name;
    private String crop;
    private String status;
    private BigDecimal temperature;
    private BigDecimal humidity;
    private LocalDateTime createTime;

    public GreenhouseListResponse(Long id, String name, String crop, String status, 
                                 BigDecimal temperature, BigDecimal humidity, LocalDateTime createTime) {
        this.id = id;
        this.name = name;
        this.crop = crop;
        this.status = status;
        this.temperature = temperature;
        this.humidity = humidity;
        this.createTime = createTime;
    }
}
