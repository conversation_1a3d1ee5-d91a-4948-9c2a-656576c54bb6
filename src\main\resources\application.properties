# 应用配置
spring.application.name=smart-agriculture
server.port=8080

# 数据库配置
spring.datasource.url=******************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=123456
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA配置
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect

# JWT配置
jwt.secret=mySecretKey123456789012345678901234567890
jwt.expiration=86400000

# 文件上传配置
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
file.upload.path=uploads/
file.upload.max-size=10485760

# 日志配置
logging.level.me.zxk.smartagriculture=DEBUG
logging.level.org.springframework.security=DEBUG
