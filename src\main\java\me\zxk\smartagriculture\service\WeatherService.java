package me.zxk.smartagriculture.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.zxk.smartagriculture.dto.weather.WeatherResponse;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 天气服务
 * 注意：这里提供模拟数据，实际项目中应该调用真实的天气API
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class WeatherService {

    /**
     * 获取天气信息
     * 注意：这里返回模拟数据，实际项目中应该调用第三方天气API
     */
    public WeatherResponse getWeatherInfo(Double latitude, Double longitude) {
        log.info("获取天气信息: 纬度={}, 经度={}", latitude, longitude);

        WeatherResponse response = new WeatherResponse();
        response.setCurrentLocation("智慧农场 东区");

        // 当前天气
        WeatherResponse.CurrentWeather currentWeather = new WeatherResponse.CurrentWeather();
        currentWeather.setTemperature(26.3);
        currentWeather.setType("晴");
        currentWeather.setHigh(29);
        currentWeather.setLow(19);
        response.setCurrentWeather(currentWeather);

        // 农业指数
        WeatherResponse.AgricultureIndex agricultureIndex = new WeatherResponse.AgricultureIndex();
        agricultureIndex.setPlanting("good");
        agricultureIndex.setIrrigation("normal");
        agricultureIndex.setSunlight("good");
        response.setAgricultureIndex(agricultureIndex);

        // 降水预报
        WeatherResponse.RainForecast rainForecast = new WeatherResponse.RainForecast();
        rainForecast.setWillRain(true);
        rainForecast.setTime(45);
        rainForecast.setIntensity("小雨");
        // 生成120个点的模拟降水数据
        List<Double> chartData = new ArrayList<>();
        for (int i = 0; i < 120; i++) {
            chartData.add(Math.random() * 0.5);
        }
        rainForecast.setChartData(chartData);
        response.setRainForecast(rainForecast);

        // 一周天气预报
        List<WeatherResponse.WeeklyForecast> weeklyForecast = Arrays.asList(
                createWeeklyForecast("今天", "12/28", "小雨", 29, 19, "rain", "#2196F3", "适宜室内作业"),
                createWeeklyForecast("明天", "12/29", "多云", 27, 18, "cloudy", "#9E9E9E", "适宜播种"),
                createWeeklyForecast("后天", "12/30", "晴", 30, 20, "sunny", "#FF9800", "适宜施肥"),
                createWeeklyForecast("周四", "12/31", "晴", 28, 17, "sunny", "#FF9800", "适宜浇水"),
                createWeeklyForecast("周五", "01/01", "阴", 25, 16, "cloudy", "#9E9E9E", "适宜除草"),
                createWeeklyForecast("周六", "01/02", "小雨", 24, 15, "rain", "#2196F3", "适宜室内作业"),
                createWeeklyForecast("周日", "01/03", "晴", 26, 18, "sunny", "#FF9800", "适宜收获")
        );
        response.setWeeklyForecast(weeklyForecast);

        // 天气详情
        WeatherResponse.WeatherDetails weatherDetails = new WeatherResponse.WeatherDetails();
        weatherDetails.setVisibility(15);
        weatherDetails.setHumidity(68);
        weatherDetails.setWindSpeed(12);
        weatherDetails.setFeelsLike(28);
        weatherDetails.setUvIndex(6);
        weatherDetails.setPressure(1013);
        response.setWeatherDetails(weatherDetails);

        return response;
    }

    /**
     * 创建一周天气预报项
     */
    private WeatherResponse.WeeklyForecast createWeeklyForecast(String day, String date, String type,
                                                               Integer high, Integer low, String icon,
                                                               String color, String advice) {
        WeatherResponse.WeeklyForecast forecast = new WeatherResponse.WeeklyForecast();
        forecast.setDay(day);
        forecast.setDate(date);
        forecast.setType(type);
        forecast.setHigh(high);
        forecast.setLow(low);
        forecast.setIcon(icon);
        forecast.setColor(color);
        forecast.setAdvice(advice);
        return forecast;
    }
}
