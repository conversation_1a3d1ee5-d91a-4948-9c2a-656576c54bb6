package me.zxk.smartagriculture.repository;

import me.zxk.smartagriculture.entity.UserSettings;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 用户设置数据访问接口
 */
@Repository
public interface UserSettingsRepository extends JpaRepository<UserSettings, Long> {

    /**
     * 根据用户ID查找用户设置
     */
    Optional<UserSettings> findByUserId(Long userId);

    /**
     * 根据用户ID删除用户设置
     */
    void deleteByUserId(Long userId);
}
