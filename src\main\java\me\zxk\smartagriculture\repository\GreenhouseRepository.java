package me.zxk.smartagriculture.repository;

import me.zxk.smartagriculture.entity.Greenhouse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 大棚数据访问接口
 */
@Repository
public interface GreenhouseRepository extends JpaRepository<Greenhouse, Long> {

    /**
     * 根据用户ID分页查询大棚列表
     */
    Page<Greenhouse> findByUserId(Long userId, Pageable pageable);

    /**
     * 根据ID和用户ID查找大棚
     */
    Optional<Greenhouse> findByIdAndUserId(Long id, Long userId);

    /**
     * 检查用户是否拥有指定名称的大棚
     */
    boolean existsByUserIdAndName(Long userId, String name);

    /**
     * 统计用户的大棚数量
     */
    long countByUserId(Long userId);
}
